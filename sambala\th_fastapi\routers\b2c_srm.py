from typing import Annotated, List
from odoo.api import Environment
from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from pydantic import BaseModel
from ..dependencies import fastapi_endpoint, odoo_env, authenticated_fastapi_endpoint
from odoo.addons.fastapi.models.fastapi_endpoint import FastapiEndpoint as ThFastapi
import time

router = APIRouter(tags=["SRM"])


# Model cho request batch
class BatchStudentCodesRequest(BaseModel):
    student_codes: List[str]


def write_log(self, data: object, state: str, duration: str, function_name: str = None, description: str = None):
    self.env['th.log.api'].create({
        'state': state,
        'th_model': str(self._name),
        'th_description': description,
        'th_input_data': str(data),
        'th_function_call': function_name,
        'is_log_fast_api': True,
        'th_fastapi_endpoint_id': self.id,
        'th_time_response': duration,
    })


@router.get("/{item_id}")
async def get_partners(fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)], env: Annotated[Environment, Depends(odoo_env)],
                       background_tasks: BackgroundTasks,
                       item_id: str):
    start = time.time()
    data = {}
    try:
        if fastapi:
            student = env['th.student'].th_get_student(item_id)
            partner_id = student.th_partner_id
            data = {
                'name': partner_id.name,
                'phone': partner_id.phone,
                'email': partner_id.email,
                'birthday': str(partner_id.th_birthday.strftime("%d/%m/%Y") if partner_id.th_birthday else ''),
                'student_code': student.th_student_code,
                'acceptance': student.th_acceptance,
                'major': student.th_major_id.name,
                'class': student.th_class,
                'fee_status': "Chưa hoàn thành" if student.th_fee_status == "undone" else "Hoàn thành",
                'gender': 'Nam' if student.th_gender == 'male' else 'Nữ' if student.th_gender == 'female' else 'Khác' if student.th_gender else '',
                'place_of_birth': partner_id.th_place_of_birth_id.name,
            }
            background_tasks.add_task(write_log, fastapi, data, 'success', str(round(time.time() - start, 4)), 'srm/get_partners')
            return data
        else:
            background_tasks.add_task(write_log, fastapi, data, 'error', str(round(time.time() - start, 4)), 'srm/get_partners', 'Không có quyền truy cập!')
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED, detail="Không có quyền truy cập!"
            )
    except Exception as e:
        write_log(fastapi, data, 'error', str(round(time.time() - start, 4)), 'srm/get_partners', str(e))
        env.cr.commit()
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED, detail="Có lỗi xảy ra vui lòng thử lại sau!"
        )

@router.post("/contacts")
async def get_partners_batch(
    request: BatchStudentCodesRequest,
    fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)], 
    env: Annotated[Environment, Depends(odoo_env)],
    background_tasks: BackgroundTasks
):
    """
    Endpoint xử lý batch 100 mã sinh viên và trả về thông tin của họ
    """
    start = time.time()
    data = []
    
    try:
        if not fastapi:
            background_tasks.add_task(write_log, fastapi, {}, 'error', str(round(time.time() - start, 4)), 
                                    'srm/get_partners_batch', 'Không có quyền truy cập!')
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED, 
                detail="Không có quyền truy cập!"
            )
        
        student_codes = request.student_codes
        if not student_codes:
            background_tasks.add_task(write_log, fastapi, {}, 'error', str(round(time.time() - start, 4)), 
                                    'srm/get_partners_batch', 'Danh sách mã sinh viên trống!')
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Danh sách mã sinh viên trống!"
            )
        
        # Giới hạn tối đa 100 mã sinh viên một lần
        if len(student_codes) > 100:
            background_tasks.add_task(write_log, fastapi, {}, 'error', str(round(time.time() - start, 4)), 
                                    'srm/get_partners_batch', 'Vượt quá giới hạn 100 mã sinh viên!')
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Vượt quá giới hạn 100 mã sinh viên một lần!"
            )
        
        # Tìm students theo danh sách mã sinh viên
        students = env['th.student'].sudo().search([('th_student_code', 'in', student_codes)])
        
        for student in students:
            partner_id = student.th_partner_id
            student_data = {
                'th_student_code': student.th_student_code,
                'name': partner_id.name if partner_id else '',
                'phone': partner_id.phone if partner_id else '',
                'email': partner_id.email if partner_id else '',
                'th_birthday': str(partner_id.th_birthday.strftime("%d/%m/%Y") if partner_id.th_birthday else '') if partner_id and partner_id.th_birthday else ''),
                'th_gender': 'Nam' if student.th_gender == 'male' else 'Nữ' if student.th_gender == 'female' else 'Khác' if student.th_gender else '',
                'th_place_of_birth': partner_id.th_place_of_birth_id.name if partner_id and partner_id.th_place_of_birth_id else '',
                'th_acceptance': student.th_acceptance if student.th_acceptance else '',
                'th_course': student.th_class if student.th_class else '',
                'th_fee_status': "Chưa hoàn thành" if student.th_fee_status == "undone" else "Hoàn thành",
                'th_major': student.th_major_id.name if student.th_major_id else '',
            }
            
            data.append(student_data)
        
        background_tasks.add_task(write_log, fastapi, f"Processed {len(data)} students", 'success', 
                                str(round(time.time() - start, 4)), 'srm/get_partners_batch')
        
        return data
        
    except HTTPException:
        raise
    except Exception as e:
        error_msg = f"Có lỗi xảy ra: {str(e)}"
        write_log(fastapi, {}, 'error', str(round(time.time() - start, 4)), 
                 'srm/get_partners_batch', error_msg)
        env.cr.commit()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, 
            detail="Có lỗi xảy ra vui lòng thử lại sau!"
        )