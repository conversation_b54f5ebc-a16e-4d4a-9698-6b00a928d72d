<?xml version="1.0" encoding="utf-8" ?>
<odoo>

    <record id="view_requeue_job" model="ir.ui.view">
        <field name="name">Requeue Jobs</field>
        <field name="model">queue.requeue.job</field>
        <field name="arch" type="xml">
            <form string="Requeue Jobs">
                <group string="The selected jobs will be requeued.">
                    <field name="job_ids" nolabel="1" colspan="2" />
                </group>
                <footer>
                    <button
                        name="requeue"
                        string="Requeue"
                        type="object"
                        class="oe_highlight"
                    />
                    <button string="Cancel" class="oe_link" special="cancel" />
                </footer>
            </form>
        </field>
    </record>

    <record id="action_requeue_job" model="ir.actions.act_window">
        <field name="name">Requeue Jobs</field>
        <field name="res_model">queue.requeue.job</field>
        <field name="view_mode">form</field>
        <field name="view_id" ref="view_requeue_job" />
        <field name="target">new</field>
        <field name="binding_model_id" ref="queue_job.model_queue_job" />
    </record>

</odoo>
