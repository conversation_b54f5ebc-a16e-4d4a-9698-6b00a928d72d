<?xml version="1.0" encoding="utf-8" ?>
<odoo>

    <record id="view_set_jobs_cancelled" model="ir.ui.view">
        <field name="name">Cancel Jobs</field>
        <field name="model">queue.jobs.to.cancelled</field>
        <field name="arch" type="xml">
            <form>
                <group string="The selected jobs will be cancelled.">
                    <field name="job_ids" nolabel="1" colspan="2" />
                </group>
                <footer>
                    <button
                        name="set_cancelled"
                        string="Cancel jobs"
                        type="object"
                        class="oe_highlight"
                    />
                    <button string="Cancel" class="oe_link" special="cancel" />
                </footer>
            </form>
        </field>
    </record>

    <record id="action_set_jobs_cancelled" model="ir.actions.act_window">
        <field name="name">Cancel jobs</field>
        <field name="res_model">queue.jobs.to.cancelled</field>
        <field name="view_mode">form</field>
        <field name="view_id" ref="view_set_jobs_cancelled" />
        <field name="target">new</field>
        <field name="binding_model_id" ref="queue_job.model_queue_job" />
    </record>

</odoo>
