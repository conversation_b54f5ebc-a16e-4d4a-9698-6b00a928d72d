# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* queue_job
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2024-04-17 09:07+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: none\n"
"Language: fr\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=n > 1;\n"
"X-Generator: Weblate 4.17\n"

#. module: queue_job
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_form
msgid ""
"<br/>\n"
"                            <span class=\"oe_grey oe_inline\"> If the max. retries is 0, the number of retries is infinite.</span>"
msgstr ""
"<br/>\n"
"                            <span class=\"oe_grey oe_inline\"> Si le maximum "
"d'essais est défini à 0, le nombre d'essais sera infini.</span>"

#. module: queue_job
#. odoo-python
#: code:addons/queue_job/controllers/main.py:0
#, python-format
msgid "Access Denied"
msgstr "Accès refusé"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__message_needaction
msgid "Action Needed"
msgstr "Action nécessaire"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__activity_ids
msgid "Activities"
msgstr "Activités"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Activité exception décoration"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__activity_state
msgid "Activity State"
msgstr "Statut de l’activité"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__activity_type_icon
msgid "Activity Type Icon"
msgstr "Icône de type d’activité"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__args
msgid "Args"
msgstr "Arguments"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__message_attachment_count
msgid "Attachment Count"
msgstr "Nombre de pièces jointes"

#. module: queue_job
#: model:ir.actions.server,name:queue_job.ir_cron_autovacuum_queue_jobs_ir_actions_server
#: model:ir.cron,cron_name:queue_job.ir_cron_autovacuum_queue_jobs
msgid "AutoVacuum Job Queue"
msgstr "Nettoyage automatique de la file d’attente des tâches"

#. module: queue_job
#: model:ir.model,name:queue_job.model_base
msgid "Base"
msgstr "Base"

#. module: queue_job
#: model_terms:ir.ui.view,arch_db:queue_job.view_requeue_job
#: model_terms:ir.ui.view,arch_db:queue_job.view_set_jobs_cancelled
#: model_terms:ir.ui.view,arch_db:queue_job.view_set_jobs_done
msgid "Cancel"
msgstr "Annuler"

#. module: queue_job
#: model:ir.model,name:queue_job.model_queue_jobs_to_cancelled
msgid "Cancel all selected jobs"
msgstr "Annuler toutes les tâches sélectionnées"

#. module: queue_job
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_form
msgid "Cancel job"
msgstr "Annuler la tâche"

#. module: queue_job
#: model:ir.actions.act_window,name:queue_job.action_set_jobs_cancelled
#: model_terms:ir.ui.view,arch_db:queue_job.view_set_jobs_cancelled
msgid "Cancel jobs"
msgstr "Annuler les tâches"

#. module: queue_job
#: model:ir.model.fields.selection,name:queue_job.selection__queue_job__state__cancelled
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_search
msgid "Cancelled"
msgstr "Annulée"

#. module: queue_job
#. odoo-python
#: code:addons/queue_job/models/queue_job.py:0
#, python-format
msgid "Cancelled by %s"
msgstr "Annulée par %s"

#. module: queue_job
#. odoo-python
#: code:addons/queue_job/models/queue_job_channel.py:0
#, python-format
msgid "Cannot change the root channel"
msgstr "Le canal racine ne peut pas être modifié"

#. module: queue_job
#. odoo-python
#: code:addons/queue_job/models/queue_job_channel.py:0
#, python-format
msgid "Cannot remove the root channel"
msgstr "Le canal racine ne peut être supprimé"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__channel
#: model:ir.model.fields,field_description:queue_job.field_queue_job_function__channel_id
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_function_search
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_search
msgid "Channel"
msgstr "Canal"

#. module: queue_job
#: model:ir.model.constraint,message:queue_job.constraint_queue_job_channel_name_uniq
msgid "Channel complete name must be unique"
msgstr "Le nom complet du canal doit être unique"

#. module: queue_job
#: model:ir.actions.act_window,name:queue_job.action_queue_job_channel
#: model:ir.ui.menu,name:queue_job.menu_queue_job_channel
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_channel_form
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_channel_search
msgid "Channels"
msgstr "Canaux"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__company_id
msgid "Company"
msgstr "Société"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__channel_method_name
msgid "Complete Method Name"
msgstr "Nom complet de la méthode"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job_channel__complete_name
#: model:ir.model.fields,field_description:queue_job.field_queue_job_function__channel
msgid "Complete Name"
msgstr "Nom complet"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__date_created
msgid "Created Date"
msgstr "Date de création"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job_channel__create_uid
#: model:ir.model.fields,field_description:queue_job.field_queue_jobs_to_cancelled__create_uid
#: model:ir.model.fields,field_description:queue_job.field_queue_jobs_to_done__create_uid
#: model:ir.model.fields,field_description:queue_job.field_queue_requeue_job__create_uid
msgid "Created by"
msgstr "Créé par"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job_channel__create_date
#: model:ir.model.fields,field_description:queue_job.field_queue_jobs_to_cancelled__create_date
#: model:ir.model.fields,field_description:queue_job.field_queue_jobs_to_done__create_date
#: model:ir.model.fields,field_description:queue_job.field_queue_requeue_job__create_date
msgid "Created on"
msgstr "Créé le"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__retry
msgid "Current try"
msgstr "Tentative actuelle"

#. module: queue_job
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_form
msgid "Current try / max. retries"
msgstr "Tentative actuelle / tentatives maximales"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__date_cancelled
msgid "Date Cancelled"
msgstr "Date d’annulation"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__date_done
msgid "Date Done"
msgstr "Date de fin"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__dependencies
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_form
msgid "Dependencies"
msgstr "Dépendances"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__dependency_graph
msgid "Dependency Graph"
msgstr "Graphe de dépendance"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__name
msgid "Description"
msgstr "Description"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__display_name
#: model:ir.model.fields,field_description:queue_job.field_queue_job_channel__display_name
#: model:ir.model.fields,field_description:queue_job.field_queue_job_function__display_name
#: model:ir.model.fields,field_description:queue_job.field_queue_jobs_to_cancelled__display_name
#: model:ir.model.fields,field_description:queue_job.field_queue_jobs_to_done__display_name
#: model:ir.model.fields,field_description:queue_job.field_queue_requeue_job__display_name
msgid "Display Name"
msgstr "Nom affiché"

#. module: queue_job
#: model:ir.model.fields.selection,name:queue_job.selection__queue_job__state__done
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_search
msgid "Done"
msgstr "Terminé"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__date_enqueued
msgid "Enqueue Time"
msgstr "Heure de mise en file d’attente"

#. module: queue_job
#: model:ir.model.fields.selection,name:queue_job.selection__queue_job__state__enqueued
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_search
msgid "Enqueued"
msgstr "Dans la file d’attente"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__exc_name
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_search
msgid "Exception"
msgstr "Exception"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__exc_info
msgid "Exception Info"
msgstr "Information sur l’exception"

#. module: queue_job
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_form
msgid "Exception Information"
msgstr "Information sur l’exception"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__exc_message
msgid "Exception Message"
msgstr "Message d’exception"

#. module: queue_job
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_search
msgid "Exception message"
msgstr "Message d’exception"

#. module: queue_job
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_form
msgid "Exception:"
msgstr "Exception :"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__eta
msgid "Execute only after"
msgstr "Exécuter uniquement après"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__exec_time
msgid "Execution Time (avg)"
msgstr "Temps d’exécution (moyenne)"

#. module: queue_job
#: model:ir.model.fields.selection,name:queue_job.selection__queue_job__state__failed
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_search
msgid "Failed"
msgstr "En erreur"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_ir_model_fields__ttype
msgid "Field Type"
msgstr "Type de champ"

#. module: queue_job
#: model:ir.model,name:queue_job.model_ir_model_fields
msgid "Fields"
msgstr "Champs"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__message_follower_ids
msgid "Followers"
msgstr "Abonnés"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__message_partner_ids
msgid "Followers (Partners)"
msgstr "Abonnés (partenaires)"

#. module: queue_job
#: model:ir.model.fields,help:queue_job.field_queue_job__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Icône Font Awesome par ex. fa-tasks"

#. module: queue_job
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_search
msgid "Graph"
msgstr "Graphe"

#. module: queue_job
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_form
msgid "Graph Jobs"
msgstr "Graphe des tâches"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__graph_jobs_count
msgid "Graph Jobs Count"
msgstr "Nombre de graphe de tâches"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__graph_uuid
msgid "Graph UUID"
msgstr "Identifiant unique du graphe"

#. module: queue_job
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_function_search
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_search
msgid "Group By"
msgstr "Regrouper par"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__has_message
msgid "Has Message"
msgstr "A un message"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__id
#: model:ir.model.fields,field_description:queue_job.field_queue_job_channel__id
#: model:ir.model.fields,field_description:queue_job.field_queue_job_function__id
#: model:ir.model.fields,field_description:queue_job.field_queue_jobs_to_cancelled__id
#: model:ir.model.fields,field_description:queue_job.field_queue_jobs_to_done__id
#: model:ir.model.fields,field_description:queue_job.field_queue_requeue_job__id
msgid "ID"
msgstr "Identifiant"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__activity_exception_icon
msgid "Icon"
msgstr "Icône"

#. module: queue_job
#: model:ir.model.fields,help:queue_job.field_queue_job__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Icône indiquant une activité d’exception."

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__identity_key
msgid "Identity Key"
msgstr "Clé d’identité"

#. module: queue_job
#. odoo-python
#: code:addons/queue_job/models/queue_job.py:0
#, python-format
msgid "If both parameters are 0, ALL jobs will be requeued!"
msgstr ""
"Si les deux paramètres sont à 0, toutes les tâches seront remises en file d’"
"attente !"

#. module: queue_job
#: model:ir.model.fields,help:queue_job.field_queue_job__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Si coché, de nouveaux messages demandent votre attention."

#. module: queue_job
#: model:ir.model.fields,help:queue_job.field_queue_job__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "Si coché, certains messages ont une erreur de livraison."

#. module: queue_job
#. odoo-python
#: code:addons/queue_job/models/queue_job_function.py:0
#, python-format
msgid "Invalid job function: {}"
msgstr "Fonction de tâche invalide : {}"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__message_is_follower
msgid "Is Follower"
msgstr "Est un abonné"

#. module: queue_job
#: model:ir.model,name:queue_job.model_queue_job_channel
msgid "Job Channels"
msgstr "Canaux de tâche"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__job_function_id
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_search
msgid "Job Function"
msgstr "Fonction de tâche"

#. module: queue_job
#: model:ir.actions.act_window,name:queue_job.action_queue_job_function
#: model:ir.model,name:queue_job.model_queue_job_function
#: model:ir.model.fields,field_description:queue_job.field_queue_job_channel__job_function_ids
#: model:ir.ui.menu,name:queue_job.menu_queue_job_function
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_function_form
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_function_search
msgid "Job Functions"
msgstr "Fonctions de tâche"

#. module: queue_job
#: model:ir.module.category,name:queue_job.module_category_queue_job
#: model:ir.ui.menu,name:queue_job.menu_queue_job_root
msgid "Job Queue"
msgstr "File d’attente de tâche"

#. module: queue_job
#: model:res.groups,name:queue_job.group_queue_job_manager
msgid "Job Queue Manager"
msgstr "Gestionnaire de file d’attente de tâche"

#. module: queue_job
#: model:ir.model.fields.selection,name:queue_job.selection__ir_model_fields__ttype__job_serialized
msgid "Job Serialized"
msgstr "Sérialisation de tâche"

#. module: queue_job
#: model:mail.message.subtype,name:queue_job.mt_job_failed
msgid "Job failed"
msgstr "Tâche en échec"

#. module: queue_job
#. odoo-python
#: code:addons/queue_job/controllers/main.py:0
#, python-format
msgid "Job interrupted and set to Done: nothing to do."
msgstr "Tâche interrompue et mise à Terminé : il n’y a rien à faire."

#. module: queue_job
#: model:ir.actions.act_window,name:queue_job.action_queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_jobs_to_cancelled__job_ids
#: model:ir.model.fields,field_description:queue_job.field_queue_jobs_to_done__job_ids
#: model:ir.model.fields,field_description:queue_job.field_queue_requeue_job__job_ids
#: model:ir.ui.menu,name:queue_job.menu_queue_job
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_form
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_graph
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_pivot
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_search
msgid "Jobs"
msgstr "Tâches"

#. module: queue_job
#: model:ir.actions.server,name:queue_job.ir_cron_queue_job_garbage_collector_ir_actions_server
#: model:ir.cron,cron_name:queue_job.ir_cron_queue_job_garbage_collector
msgid "Jobs Garbage Collector"
msgstr "Ramasse-miette des tâches"

#. module: queue_job
#. odoo-python
#: code:addons/queue_job/models/queue_job.py:0
#, python-format
msgid "Jobs for graph %s"
msgstr "Tâches pour le graphe %s"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__kwargs
msgid "Kwargs"
msgstr "Arguments par clé"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job____last_update
#: model:ir.model.fields,field_description:queue_job.field_queue_job_channel____last_update
#: model:ir.model.fields,field_description:queue_job.field_queue_job_function____last_update
#: model:ir.model.fields,field_description:queue_job.field_queue_jobs_to_cancelled____last_update
#: model:ir.model.fields,field_description:queue_job.field_queue_jobs_to_done____last_update
#: model:ir.model.fields,field_description:queue_job.field_queue_requeue_job____last_update
msgid "Last Modified on"
msgstr "Dernière modification le"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job_channel__write_uid
#: model:ir.model.fields,field_description:queue_job.field_queue_jobs_to_cancelled__write_uid
#: model:ir.model.fields,field_description:queue_job.field_queue_jobs_to_done__write_uid
#: model:ir.model.fields,field_description:queue_job.field_queue_requeue_job__write_uid
msgid "Last Updated by"
msgstr "Dernière mise à jour par"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job_channel__write_date
#: model:ir.model.fields,field_description:queue_job.field_queue_jobs_to_cancelled__write_date
#: model:ir.model.fields,field_description:queue_job.field_queue_jobs_to_done__write_date
#: model:ir.model.fields,field_description:queue_job.field_queue_requeue_job__write_date
msgid "Last Updated on"
msgstr "Dernière mise à jour le"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__message_main_attachment_id
msgid "Main Attachment"
msgstr "Attachement principal"

#. module: queue_job
#. odoo-python
#: code:addons/queue_job/models/queue_job.py:0
#, python-format
msgid "Manually set to done by %s"
msgstr "Mis en terminé manuellement par %s"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__max_retries
msgid "Max. retries"
msgstr "Tentatives maximales"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__message_has_error
msgid "Message Delivery error"
msgstr "Erreur d’envoi du message"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__message_ids
msgid "Messages"
msgstr "Messages"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job_function__method
msgid "Method"
msgstr "Méthode"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__method_name
msgid "Method Name"
msgstr "Nom de la méthode"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__model_name
#: model:ir.model.fields,field_description:queue_job.field_queue_job_function__model_id
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_search
msgid "Model"
msgstr "Modèle"

#. module: queue_job
#. odoo-python
#: code:addons/queue_job/models/queue_job_function.py:0
#, python-format
msgid "Model {} not found"
msgstr "Modèle {} non trouvé"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Échéance de mon activité"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job_channel__name
#: model:ir.model.fields,field_description:queue_job.field_queue_job_function__name
msgid "Name"
msgstr "Nom"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Date limite de l’activité à venir"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__activity_summary
msgid "Next Activity Summary"
msgstr "Résumé de l’activité suivante"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__activity_type_id
msgid "Next Activity Type"
msgstr "Type de l'activité suivante"

#. module: queue_job
#. odoo-python
#: code:addons/queue_job/models/queue_job.py:0
#, python-format
msgid "No action available for this job"
msgstr "Pas d’action disponible pour cette tâche"

#. module: queue_job
#. odoo-python
#: code:addons/queue_job/models/queue_job.py:0
#, python-format
msgid "Not allowed to change field(s): {}"
msgstr "Non autorisé à changer le ou les champs : {}"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__message_needaction_counter
msgid "Number of Actions"
msgstr "Nombre d’actions"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__message_has_error_counter
msgid "Number of errors"
msgstr "Nombre d’erreurs"

#. module: queue_job
#: model:ir.model.fields,help:queue_job.field_queue_job__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Nombre de messages nécessitant une action"

#. module: queue_job
#: model:ir.model.fields,help:queue_job.field_queue_job__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Nombre de messages avec des erreurs d’envoi"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job_channel__parent_id
msgid "Parent Channel"
msgstr "Canal parent"

#. module: queue_job
#. odoo-python
#: code:addons/queue_job/models/queue_job_channel.py:0
#, python-format
msgid "Parent channel required."
msgstr "Canal parent requis."

#. module: queue_job
#: model:ir.model.fields,help:queue_job.field_queue_job_function__edit_retry_pattern
msgid ""
"Pattern expressing from the count of retries on retryable errors, the number of of seconds to postpone the next execution. Setting the number of seconds to a 2-element tuple or list will randomize the retry interval between the 2 values.\n"
"Example: {1: 10, 5: 20, 10: 30, 15: 300}.\n"
"Example: {1: (1, 10), 5: (11, 20), 10: (21, 30), 15: (100, 300)}.\n"
"See the module description for details."
msgstr ""
"Modèle indiquant le nombre de secondes de délai de la prochaine exécution à "
"partir du nombre de tentative d’essai sur les erreurs pouvant être tentées à "
"nouveau. Indiquer un couple ou une liste pour le nombre de seconde va rendre "
"aléatoire la sélection de l’intervalle parmi les valeurs.\n"
"Exemple : {1: 10, 5: 20, 10: 30, 15: 300}.\n"
"Exemple : {1: (1, 10), 5: (11, 20), 10: (21, 30), 15: (100, 300)}.\n"
"Voir la description du module pour les détails."

#. module: queue_job
#: model:ir.model.fields.selection,name:queue_job.selection__queue_job__state__pending
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_search
msgid "Pending"
msgstr "En attente"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__priority
msgid "Priority"
msgstr "Priorité"

#. module: queue_job
#: model:ir.ui.menu,name:queue_job.menu_queue
msgid "Queue"
msgstr "File d’attente"

#. module: queue_job
#: model:ir.model,name:queue_job.model_queue_job
msgid "Queue Job"
msgstr "Tâche en file d’attente"

#. module: queue_job
#. odoo-python
#: code:addons/queue_job/models/queue_job.py:0
#, python-format
msgid "Queue jobs must be created by calling 'with_delay()'."
msgstr ""
"Les tâches de file d’attente doivent être créé en appelant « with_delay() »."

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__record_ids
msgid "Record"
msgstr "Enregistrement"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__records
msgid "Record(s)"
msgstr "Enregistrement(s)"

#. module: queue_job
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_form
msgid "Related"
msgstr "Rattaché"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job_function__edit_related_action
msgid "Related Action"
msgstr "Action rattachée"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job_function__related_action
msgid "Related Action (serialized)"
msgstr "Action rattaché (sérialisé)"

#. module: queue_job
#. odoo-python
#: code:addons/queue_job/models/queue_job.py:0
#, python-format
msgid "Related Record"
msgstr "Enregistrement rattaché"

#. module: queue_job
#. odoo-python
#: code:addons/queue_job/models/queue_job.py:0
#, python-format
msgid "Related Records"
msgstr "Enregistrements rattachés"

#. module: queue_job
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_tree
msgid "Remaining days to execute"
msgstr "Jours restants d’exécution"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job_channel__removal_interval
msgid "Removal Interval"
msgstr "Intervalle de suppression"

#. module: queue_job
#: model_terms:ir.ui.view,arch_db:queue_job.view_requeue_job
msgid "Requeue"
msgstr "Remettre dans la file d’attente"

#. module: queue_job
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_form
msgid "Requeue Job"
msgstr "Remettre la tâche dans la file d’attente"

#. module: queue_job
#: model:ir.actions.act_window,name:queue_job.action_requeue_job
#: model_terms:ir.ui.view,arch_db:queue_job.view_requeue_job
msgid "Requeue Jobs"
msgstr "Remettre les tâches dans la file d’attente"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__activity_user_id
msgid "Responsible User"
msgstr "Utilisateur responsable"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__result
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_form
msgid "Result"
msgstr "Résultat"

#. module: queue_job
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_form
msgid "Results"
msgstr "Résultats"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job_function__edit_retry_pattern
msgid "Retry Pattern"
msgstr "Modèle des tentatives"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job_function__retry_pattern
msgid "Retry Pattern (serialized)"
msgstr "Modèle des tentatives (sérialisé)"

#. module: queue_job
#: model:ir.model,name:queue_job.model_queue_jobs_to_done
msgid "Set all selected jobs to done"
msgstr "Mettre toutes les tâches sélectionnées en terminé"

#. module: queue_job
#: model_terms:ir.ui.view,arch_db:queue_job.view_set_jobs_done
msgid "Set jobs done"
msgstr "Mettre les tâches terminées"

#. module: queue_job
#: model:ir.actions.act_window,name:queue_job.action_set_jobs_done
msgid "Set jobs to done"
msgstr "Mettre les tâches en terminé"

#. module: queue_job
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_form
msgid "Set to 'Done'"
msgstr "Mis en terminé"

#. module: queue_job
#: model_terms:ir.ui.view,arch_db:queue_job.view_set_jobs_done
msgid "Set to done"
msgstr "Mis en terminé"

#. module: queue_job
#: model:ir.model.fields,help:queue_job.field_queue_job__graph_uuid
msgid "Single shared identifier of a Graph. Empty for a single job."
msgstr "Identifiant unique partagé d’un graphe. Vide pour une tâche unique."

#. module: queue_job
#. odoo-python
#: code:addons/queue_job/models/queue_job.py:0
#, python-format
msgid ""
"Something bad happened during the execution of job %s. More details in the "
"'Exception Information' section."
msgstr ""
"Un problème a été rencontré lors de l’exécution de la tâche %s. La section « "
"Information d’exception » contient plus de détails."

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__date_started
msgid "Start Date"
msgstr "Date de début"

#. module: queue_job
#: model:ir.model.fields.selection,name:queue_job.selection__queue_job__state__started
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_search
msgid "Started"
msgstr "Démarré"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__state
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_search
msgid "State"
msgstr "État"

#. module: queue_job
#: model:ir.model.fields,help:queue_job.field_queue_job__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Statut basé sur les activités\n"
"En retard : la date d’échéance est déjà dépassée\n"
"Aujourd’hui : la date d'activité est aujourd’hui\n"
"Planifiée : activités futures."

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__func_string
msgid "Task"
msgstr "Tâche"

#. module: queue_job
#: model:ir.model.fields,help:queue_job.field_queue_job_function__edit_related_action
msgid ""
"The action when the button *Related Action* is used on a job. The default action is to open the view of the record related to the job. Configured as a dictionary with optional keys: enable, func_name, kwargs.\n"
"See the module description for details."
msgstr ""
"L’action lorsque le bouton « Action rattachée » est utilisé dans une tâche. "
"L’action par défaut est d’ouvrir une vue de l’enregistrement rattaché à la "
"tâche. Configuré avec un dictionnaire avec les clés optionnelles : enable, "
"func_name, kwargs.\n"
"Voir la description du module pour plus de détails."

#. module: queue_job
#: model:ir.model.fields,help:queue_job.field_queue_job__max_retries
msgid ""
"The job will fail if the number of tries reach the max. retries.\n"
"Retries are infinite when empty."
msgstr ""
"La tâche passera en échec si le nombre de tentative atteint le nombre de "
"tentatives maximales.\n"
"Les tentatives sont infinies si vide."

#. module: queue_job
#: model_terms:ir.ui.view,arch_db:queue_job.view_set_jobs_cancelled
msgid "The selected jobs will be cancelled."
msgstr "Les tâches sélectionnées seront annulées."

#. module: queue_job
#: model_terms:ir.ui.view,arch_db:queue_job.view_requeue_job
msgid "The selected jobs will be requeued."
msgstr "Les tâches sélectionnées sont remises en file d’attente."

#. module: queue_job
#: model_terms:ir.ui.view,arch_db:queue_job.view_set_jobs_done
msgid "The selected jobs will be set to done."
msgstr "Les tâches sélectionnées seront mise à l’état terminé."

#. module: queue_job
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_form
msgid "Time (s)"
msgstr "Heure(s)"

#. module: queue_job
#: model:ir.model.fields,help:queue_job.field_queue_job__exec_time
msgid "Time required to execute this job in seconds. Average when grouped."
msgstr ""
"Temps requis pour exécuter cette tâche en seconde. Moyenne lors des "
"regroupements."

#. module: queue_job
#: model:ir.model.fields,help:queue_job.field_queue_job__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Type d'activité d'exception enregistrée."

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__uuid
msgid "UUID"
msgstr "UUID"

#. module: queue_job
#. odoo-python
#: code:addons/queue_job/models/queue_job_function.py:0
#, python-format
msgid ""
"Unexpected format of Related Action for {}.\n"
"Example of valid format:\n"
"{{\"enable\": True, \"func_name\": \"related_action_foo\", \"kwargs\" {{\"limit\": 10}}}}"
msgstr ""
"Format inattendu pour l’action rattachée de {}.\n"
"Exemple de format valide :\n"
"{{\"enable\": True, \"func_name\": \"related_action_foo\", \"kwargs\" {{"
"\"limit\": 10}}}}"

#. module: queue_job
#. odoo-python
#: code:addons/queue_job/models/queue_job_function.py:0
#, python-format
msgid ""
"Unexpected format of Retry Pattern for {}.\n"
"Example of valid format:\n"
"{{1: 300, 5: 600, 10: 1200, 15: 3000}}"
msgstr ""
"Format inattendu pour le modèle des tentatives de {}.\n"
"Exemple de format valide :\n"
"{{1: 300, 5: 600, 10: 1200, 15: 3000}}"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__user_id
msgid "User ID"
msgstr "Identifiant utilisateur"

#. module: queue_job
#: model:ir.model.fields.selection,name:queue_job.selection__queue_job__state__wait_dependencies
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_search
msgid "Wait Dependencies"
msgstr "Dépendances en attente"

#. module: queue_job
#: model:ir.model,name:queue_job.model_queue_requeue_job
msgid "Wizard to requeue a selection of jobs"
msgstr "Assistant de remise de tâches en file d’attente"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__worker_pid
msgid "Worker Pid"
msgstr "Identifiant du processus de travail"
