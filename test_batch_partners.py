"""
Test file cho endpoint batch partners
"""

import requests
import json

# Ví dụ test endpoint batch partners
def test_batch_partners():
    url = "http://localhost:8000/partners/batch"  # Thay đổi URL cho phù hợp
    headers = {
        "api-key": "your-api-key-here",
        "Content-Type": "application/json"
    }
    
    # Data mẫu với 5 mã sinh viên
    payload = {
        "student_codes": [
            "SV001", "SV002", "SV003", "SV004", "SV005"
        ]
    }
    
    try:
        response = requests.post(url, headers=headers, json=payload)
        response.raise_for_status()
        
        if response.status_code == 200:
            data = response.json()
            print(f"Nhận được {len(data)} bản ghi:")
            for record in data:
                print(f"- {record.get('th_student_code')}: {record.get('name')}")
        else:
            print(f"Lỗi: {response.status_code}")
            
    except Exception as e:
        print(f"Lỗi khi gọi API: {e}")

if __name__ == "__main__":
    test_batch_partners()
