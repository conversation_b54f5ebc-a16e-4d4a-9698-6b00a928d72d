from odoo import api, fields, models
from odoo.exceptions import ValidationError, UserError
from odoo.models import BaseModel
import requests
import json


class ResPartner(models.Model):
    _inherit = 'res.partner'

    @api.model
    def th_process_sync_partner(self):
        th_api = self.env['th.api.server'].search([('state', '=', 'deploy'), ('th_type', '=', 'samp')], limit=1, order='id desc')
        if not th_api:
            raise UserError("No active API server configuration found for 'system'.")

        headers = {
            "api-key": th_api.th_api_key or "",
            "Content-Type": "application/json"
        }
        
        # L<PERSON>y danh sách mã sinh viên các bản ghi cần đồng bộ
        partners_to_sync = self.search([('th_student_code', '!=', False)])
        student_codes = [partner.th_student_code for partner in partners_to_sync if partner.th_student_code]
        
        # Chia danh sách thành các batch 100 mã sinh viên
        batch_size = 100
        total_synced = 0
        
        for i in range(0, len(student_codes), batch_size):
            batch_codes = student_codes[i:i + batch_size]
            
            try:
                url = f"{th_api.th_url_api}/v1/contacts"
                
                payload = {
                    "student_codes": batch_codes
                }
                
                response = requests.post(url, headers=headers, json=payload)
                response.raise_for_status()
                
                if response.status_code == 200:
                    data = response.json()
                    for record in data:
                        # Tìm partner theo mã sinh viên
                        partner = self.search([('th_student_code', '=', record.get('th_student_code'))], limit=1)
                        if partner:
                            partner_data = {
                                'name': record.get('name'),
                                'phone': record.get('phone'),
                                'email': record.get('email'),
                                'th_gender': record.get('th_gender'),
                                'th_birthday': record.get('th_birthday'),
                                'th_place_of_birth': record.get('th_place_of_birth'),
                                'th_acceptance': record.get('th_acceptance'),
                                'th_course': record.get('th_course'),
                                'th_fee_status': record.get('th_fee_status'),
                                'th_major': record.get('th_major'),
                            }
                            # Loại bỏ các giá trị None hoặc False
                            partner_data = {k: v for k, v in partner_data.items() if v is not None and v is not False}
                            partner.write(partner_data)
                            total_synced += 1
                    
                    self.env.cr.commit()
            except Exception as e:
                self.env.cr.rollback()
                raise UserError(f"Failed to sync batch {i//batch_size + 1}: {e}")
        
        return {
            "status": "success",
            "message": f"Đồng bộ thành công {total_synced}/{len(student_codes)} bản ghi từ {total_batches} batch.",
            "total_synced": total_synced,
            "total_records": len(student_codes),
            "total_batches": total_batches
        }
            


