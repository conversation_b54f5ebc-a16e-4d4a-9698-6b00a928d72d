# Quy tắc lập trình <PERSON>o cơ bản

## 1. <PERSON>uy ước đặt tên biến và hàm

* Sử dụng tiền tố `th_` (viết tắt của "thực hành" hoặc tên module/công ty) cho các biến và hàm không phải là thành phần cơ bản của Odoo. Điều này giúp phân biệt mã tùy chỉnh với mã lõi của Odoo và tránh xung đột tên.

    **Ví dụ:**
    ```python
    # Biến tùy chỉnh
    th_customer_code = fields.Char('Mã khách hàng')

    # Hàm tùy chỉnh
    def th_calculate_discount(self):
        # Logic tính toán chiết khấu tùy chỉnh
        pass
    ```

## 2. Ngôn ngữ bình luận mã

* Sử dụng tiếng Việt để bình luận mã. Điều này giúp đồng nghiệp và những người khác dễ dàng hiểu mục đích và logic của mã hơn, đặc biệt trong môi trường phát triển tại Việt Nam.

    **Ví dụ:**
    ```python
    # Phương thức này dùng để cập nhật trạng thái đơn hàng sau khi thanh toán thành công
    def action_th_confirm_payment(self):
        # Kiểm tra điều kiện thanh toán
        if self.state == 'draft':
            self.state = 'paid'
            # Ghi lại thời gian thanh toán
            self.payment_date = fields.Datetime.now()
    ```